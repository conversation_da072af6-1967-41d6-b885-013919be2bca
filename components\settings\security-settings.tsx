'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { SettingCategory } from '@/types/settings';
import { 
  Shield, 
  Smartphone, 
  Key, 
  Activity, 
  RotateCcw, 
  Plus, 
  Trash2, 
  Copy, 
  Eye, 
  EyeOff,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface SecuritySettingsProps {
  getSetting: (category: SettingCategory, key: string) => any;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  onReset: () => void;
}

export function SecuritySettings({
  getSetting,
  updateSetting,
  onReset,
}: SecuritySettingsProps) {
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [newApiKeyName, setNewApiKeyName] = useState('');
  const [showNewApiKeyDialog, setShowNewApiKeyDialog] = useState(false);

  // Get current settings with defaults
  const security = getSetting('security', 'settings') || {
    twoFactor: {
      enabled: false,
      method: 'app',
      backupCodes: [],
    },
    sessions: {
      maxActiveSessions: 5,
      requireReauth: false,
      logSuspiciousActivity: true,
    },
    apiKeys: {
      enabled: false,
      keys: [],
    },
    auditLog: {
      enabled: true,
      retentionDays: 90,
      events: ['login', 'logout', 'password_change', 'role_change'],
    },
  };

  const updateTwoFactor = async (field: string, value: any) => {
    await updateSetting('security', 'settings', {
      ...security,
      twoFactor: {
        ...security.twoFactor,
        [field]: value,
      },
    });
  };

  const updateSessions = async (field: string, value: any) => {
    await updateSetting('security', 'settings', {
      ...security,
      sessions: {
        ...security.sessions,
        [field]: value,
      },
    });
  };

  const updateApiKeys = async (field: string, value: any) => {
    await updateSetting('security', 'settings', {
      ...security,
      apiKeys: {
        ...security.apiKeys,
        [field]: value,
      },
    });
  };

  const updateAuditLog = async (field: string, value: any) => {
    await updateSetting('security', 'settings', {
      ...security,
      auditLog: {
        ...security.auditLog,
        [field]: value,
      },
    });
  };

  const generateBackupCodes = () => {
    const codes = Array.from({ length: 8 }, () => 
      Math.random().toString(36).substring(2, 8).toUpperCase()
    );
    updateTwoFactor('backupCodes', codes);
  };

  const createApiKey = () => {
    if (!newApiKeyName.trim()) return;
    
    const newKey = {
      id: Math.random().toString(36).substring(2),
      name: newApiKeyName,
      key: `sk_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
      permissions: ['read'],
      created: new Date().toISOString(),
      lastUsed: null,
    };

    updateApiKeys('keys', [...security.apiKeys.keys, newKey]);
    setNewApiKeyName('');
    setShowNewApiKeyDialog(false);
  };

  const deleteApiKey = (keyId: string) => {
    const updatedKeys = security.apiKeys.keys.filter((key: any) => key.id !== keyId);
    updateApiKeys('keys', updatedKeys);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="space-y-6">
      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Two-Factor Authentication
              </CardTitle>
              <CardDescription>
                Add an extra layer of security to your account.
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {security.twoFactor.enabled && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Enabled
                </Badge>
              )}
              <Button variant="outline" size="sm" onClick={onReset}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Enable Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Require a second form of authentication when signing in
              </p>
            </div>
            <Switch
              checked={security.twoFactor.enabled}
              onCheckedChange={(checked) => updateTwoFactor('enabled', checked)}
            />
          </div>

          {security.twoFactor.enabled && (
            <>
              <Separator />
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Authentication Method</Label>
                  <Select
                    value={security.twoFactor.method}
                    onValueChange={(value) => updateTwoFactor('method', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="app">Authenticator App</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Backup Codes</Label>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowBackupCodes(!showBackupCodes)}
                      >
                        {showBackupCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={generateBackupCodes}
                      >
                        Generate New
                      </Button>
                    </div>
                  </div>
                  
                  {showBackupCodes && security.twoFactor.backupCodes.length > 0 && (
                    <div className="p-3 bg-muted rounded-md">
                      <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                        {security.twoFactor.backupCodes.map((code: string, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-background rounded">
                            <span>{code}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(code)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Store these codes safely. Each can only be used once.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Session Management
          </CardTitle>
          <CardDescription>
            Control active sessions and authentication requirements.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Maximum Active Sessions</Label>
              <span className="text-sm text-muted-foreground">
                {security.sessions.maxActiveSessions}
              </span>
            </div>
            <Slider
              value={[security.sessions.maxActiveSessions]}
              onValueChange={(value) => updateSessions('maxActiveSessions', value[0])}
              min={1}
              max={10}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>1 session</span>
              <span>10 sessions</span>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-normal">Require re-authentication</Label>
                <p className="text-sm text-muted-foreground">
                  Ask for password before sensitive actions
                </p>
              </div>
              <Switch
                checked={security.sessions.requireReauth}
                onCheckedChange={(checked) => updateSessions('requireReauth', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-normal">Log suspicious activity</Label>
                <p className="text-sm text-muted-foreground">
                  Monitor and alert on unusual login patterns
                </p>
              </div>
              <Switch
                checked={security.sessions.logSuspiciousActivity}
                onCheckedChange={(checked) => updateSessions('logSuspiciousActivity', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Keys */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                API Keys
              </CardTitle>
              <CardDescription>
                Manage API keys for programmatic access.
              </CardDescription>
            </div>
            <Switch
              checked={security.apiKeys.enabled}
              onCheckedChange={(checked) => updateApiKeys('enabled', checked)}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {security.apiKeys.enabled && (
            <>
              <div className="flex justify-between items-center">
                <Label>Active API Keys</Label>
                <Dialog open={showNewApiKeyDialog} onOpenChange={setShowNewApiKeyDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Key
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create API Key</DialogTitle>
                      <DialogDescription>
                        Give your API key a descriptive name to help you identify it later.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="keyName">Key Name</Label>
                        <Input
                          id="keyName"
                          value={newApiKeyName}
                          onChange={(e) => setNewApiKeyName(e.target.value)}
                          placeholder="e.g., Mobile App, Integration Service"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setShowNewApiKeyDialog(false)}>
                          Cancel
                        </Button>
                        <Button onClick={createApiKey} disabled={!newApiKeyName.trim()}>
                          Create Key
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {security.apiKeys.keys.length > 0 ? (
                <div className="space-y-3">
                  {security.apiKeys.keys.map((key: any) => (
                    <div key={key.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="font-medium">{key.name}</div>
                          <div className="text-sm text-muted-foreground font-mono">
                            {key.key.substring(0, 12)}...
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Created: {new Date(key.created).toLocaleDateString()}
                            {key.lastUsed && ` • Last used: ${new Date(key.lastUsed).toLocaleDateString()}`}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(key.key)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteApiKey(key.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  No API keys created yet
                </div>
              )}

              <div className="p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-md">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
                  <p className="text-sm text-amber-800 dark:text-amber-200">
                    <strong>Security Note:</strong> API keys provide full access to your account. 
                    Keep them secure and rotate them regularly.
                  </p>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Audit Log */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Audit Log
          </CardTitle>
          <CardDescription>
            Track security events and account activity.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Enable audit logging</Label>
              <p className="text-sm text-muted-foreground">
                Keep a record of important security events
              </p>
            </div>
            <Switch
              checked={security.auditLog.enabled}
              onCheckedChange={(checked) => updateAuditLog('enabled', checked)}
            />
          </div>

          {security.auditLog.enabled && (
            <>
              <Separator />
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>Log Retention</Label>
                  <span className="text-sm text-muted-foreground">
                    {security.auditLog.retentionDays} days
                  </span>
                </div>
                <Slider
                  value={[security.auditLog.retentionDays]}
                  onValueChange={(value) => updateAuditLog('retentionDays', value[0])}
                  min={30}
                  max={365}
                  step={30}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>30 days</span>
                  <span>1 year</span>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 